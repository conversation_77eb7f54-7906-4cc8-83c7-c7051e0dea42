<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>门店课件API测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .config-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .config-group {
            flex: 1;
            min-width: 200px;
        }

        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }

        .config-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
        }

        .api-section {
            padding: 30px;
        }

        .api-item {
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .api-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .api-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .api-description {
            color: #6c757d;
            font-size: 0.9em;
        }

        .api-body {
            padding: 20px;
        }

        .method-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
            margin-right: 10px;
        }

        .method-post { background: #28a745; color: white; }
        .method-get { background: #007bff; color: white; }
        .method-put { background: #ffc107; color: black; }
        .method-delete { background: #dc3545; color: white; }

        .params-section {
            margin-bottom: 20px;
        }

        .params-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #495057;
        }

        .param-group {
            margin-bottom: 15px;
        }

        .param-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }

        .param-group input,
        .param-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 14px;
            font-family: 'Courier New', monospace;
        }

        .param-group textarea {
            height: 120px;
            resize: vertical;
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .response-section {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }

        .response-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #495057;
        }

        .response-content {
            background: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .loading {
            color: #007bff;
            font-style: italic;
        }

        .error {
            color: #dc3545;
        }

        .success {
            color: #28a745;
        }

        .status-code {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: 600;
            margin-right: 10px;
        }

        .status-200 { background: #d4edda; color: #155724; }
        .status-400 { background: #f8d7da; color: #721c24; }
        .status-500 { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>门店课件API测试页面</h1>
            <p>测试WebStoreCoursewareDataController中的所有REST API接口</p>
        </div>

        <div class="config-section">
            <div class="config-row">
                <div class="config-group">
                    <label for="baseUrl">基础URL:</label>
                    <input type="text" id="baseUrl" value="http://localhost:8080" placeholder="http://localhost:8080">
                </div>
                <div class="config-group">
                    <label for="authToken">Authorization Token:</label>
                    <input type="text" id="authToken" placeholder="Bearer your-token-here">
                </div>
            </div>
        </div>

        <div class="api-section">
            <!-- API 1: 创建课件副本 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-post">POST</span>
                        创建课件副本
                    </div>
                    <div class="api-description">门店复制当前环节的课件信息，每个账号每个课件环节只能创建一个副本</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">请求参数 (JSON):</div>
                        <div class="param-group">
                            <label for="createCoursewareCopy-body">CoursewareCopyDTO:</label>
                            <textarea id="createCoursewareCopy-body" placeholder='{"coursewareId": 1, "stepId": 1, "storeId": 1}'></textarea>
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('createCoursewareCopy')">测试接口</button>
                    <div class="response-section" id="createCoursewareCopy-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="createCoursewareCopy-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 2: 查询所有教学环节 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        查询所有教学环节
                    </div>
                    <div class="api-description">查询并展示当前课件下的所有教学环节</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">查询参数:</div>
                        <div class="param-group">
                            <label for="listStoreCoursewareSteps-coursewareId">coursewareId:</label>
                            <input type="number" id="listStoreCoursewareSteps-coursewareId" placeholder="课件ID">
                        </div>
                        <div class="param-group">
                            <label for="listStoreCoursewareSteps-storeId">storeId:</label>
                            <input type="number" id="listStoreCoursewareSteps-storeId" placeholder="门店ID">
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('listStoreCoursewareSteps')">测试接口</button>
                    <div class="response-section" id="listStoreCoursewareSteps-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="listStoreCoursewareSteps-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 3: 查询门店课件环节详情 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        查询门店课件环节详情
                    </div>
                    <div class="api-description">查询门店创建的副本课件环节详细信息，确保门店只能查看自己创建的副本课件</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">查询参数:</div>
                        <div class="param-group">
                            <label for="getStoreStepDetails-storeCoursewareDataId">storeCoursewareDataId:</label>
                            <input type="number" id="getStoreStepDetails-storeCoursewareDataId" placeholder="门店课件数据ID">
                        </div>
                        <div class="param-group">
                            <label for="getStoreStepDetails-stepId">stepId:</label>
                            <input type="number" id="getStoreStepDetails-stepId" placeholder="环节ID">
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('getStoreStepDetails')">测试接口</button>
                    <div class="response-section" id="getStoreStepDetails-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="getStoreStepDetails-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 4: 编辑保存门店课件环节详情 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        编辑保存门店课件环节详情
                    </div>
                    <div class="api-description">门店编辑修改自己创建的副本课件环节详情。submitForUse=true时为'保存并提交'，将课件设置为使用状态；submitForUse=false或null时仅保存，不改变使用状态</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">请求参数 (JSON):</div>
                        <div class="param-group">
                            <label for="updateStoreStepDetails-body">StoreCoursewareDataStepDetailsDTO:</label>
                            <textarea id="updateStoreStepDetails-body" placeholder='{"storeCoursewareDataId": 1, "stepId": 1, "stepContent": "更新的环节内容", "submitForUse": false}'></textarea>
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('updateStoreStepDetails')">测试接口</button>
                    <div class="response-section" id="updateStoreStepDetails-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="updateStoreStepDetails-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 5: 更新门店课件使用状态 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        更新门店课件使用状态
                    </div>
                    <div class="api-description">单独控制门店副本课件的启用/禁用状态，确保门店只能修改自己创建的副本课件状态</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">请求参数 (JSON):</div>
                        <div class="param-group">
                            <label for="updateStoreCoursewareStatus-body">StoreCoursewareDataStatusDTO:</label>
                            <textarea id="updateStoreCoursewareStatus-body" placeholder='{"storeCoursewareDataId": 1, "status": 1}'></textarea>
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('updateStoreCoursewareStatus')">测试接口</button>
                    <div class="response-section" id="updateStoreCoursewareStatus-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="updateStoreCoursewareStatus-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 6: 恢复门店课件为标准版内容 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        恢复门店课件为标准版内容
                    </div>
                    <div class="api-description">将门店副本课件内容恢复为总部标准版最新内容，保持门店副本身份标识不变，仅同步内容数据</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">请求参数 (JSON):</div>
                        <div class="param-group">
                            <label for="restoreStoreCoursewareToStandard-body">StoreCoursewareRestoreDTO:</label>
                            <textarea id="restoreStoreCoursewareToStandard-body" placeholder='{"storeCoursewareDataId": 1}'></textarea>
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('restoreStoreCoursewareToStandard')">测试接口</button>
                    <div class="response-section" id="restoreStoreCoursewareToStandard-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="restoreStoreCoursewareToStandard-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 7: 门店副本课件预览 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-get">GET</span>
                        门店副本课件预览
                    </div>
                    <div class="api-description">预览门店创建的副本课件内容，返回与标准课件预览相同的数据结构</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">查询参数:</div>
                        <div class="param-group">
                            <label for="viewStoreCourseware-storeCoursewareDataId">storeCoursewareDataId:</label>
                            <input type="number" id="viewStoreCourseware-storeCoursewareDataId" placeholder="门店课件数据ID">
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('viewStoreCourseware')">测试接口</button>
                    <div class="response-section" id="viewStoreCourseware-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="viewStoreCourseware-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 8: 新增门店教学环节 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-post">POST</span>
                        新增门店教学环节
                    </div>
                    <div class="api-description">门店自己创建的课件副本可以新增教学环节，确保只能操作门店自己创建的副本</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">请求参数 (JSON):</div>
                        <div class="param-group">
                            <label for="addStoreCoursewareStep-body">StoreCoursewareStepDTO:</label>
                            <textarea id="addStoreCoursewareStep-body" placeholder='{"storeCoursewareDataId": 1, "stepName": "新环节名称", "stepContent": "环节内容", "stepOrder": 1}'></textarea>
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('addStoreCoursewareStep')">测试接口</button>
                    <div class="response-section" id="addStoreCoursewareStep-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="addStoreCoursewareStep-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 9: 修改门店教学环节 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        修改门店教学环节
                    </div>
                    <div class="api-description">门店自己创建的课件副本可以修改教学环节，确保只能操作门店自己创建的副本</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">请求参数 (JSON):</div>
                        <div class="param-group">
                            <label for="updateStoreCoursewareStep-body">StoreCoursewareStepDTO:</label>
                            <textarea id="updateStoreCoursewareStep-body" placeholder='{"stepId": 1, "storeCoursewareDataId": 1, "stepName": "修改的环节名称", "stepContent": "修改的环节内容"}'></textarea>
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('updateStoreCoursewareStep')">测试接口</button>
                    <div class="response-section" id="updateStoreCoursewareStep-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="updateStoreCoursewareStep-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 10: 调整门店教学环节顺序 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-put">PUT</span>
                        调整门店教学环节顺序
                    </div>
                    <div class="api-description">门店自己创建的课件副本可以调整教学环节顺序，确保只能操作门店自己创建的副本</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">请求参数 (JSON):</div>
                        <div class="param-group">
                            <label for="updateStoreCoursewareStepOrder-body">StoreCoursewareStepOrderDTO:</label>
                            <textarea id="updateStoreCoursewareStepOrder-body" placeholder='{"storeCoursewareDataId": 1, "stepOrderList": [{"stepId": 1, "stepOrder": 2}, {"stepId": 2, "stepOrder": 1}]}'></textarea>
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('updateStoreCoursewareStepOrder')">测试接口</button>
                    <div class="response-section" id="updateStoreCoursewareStepOrder-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="updateStoreCoursewareStepOrder-content"></div>
                    </div>
                </div>
            </div>

            <!-- API 11: 删除门店教学环节 -->
            <div class="api-item">
                <div class="api-header">
                    <div class="api-title">
                        <span class="method-badge method-delete">DELETE</span>
                        删除门店教学环节
                    </div>
                    <div class="api-description">门店自己创建的课件副本可以删除教学环节，确保只能操作门店自己创建的副本</div>
                </div>
                <div class="api-body">
                    <div class="params-section">
                        <div class="params-title">请求参数 (JSON):</div>
                        <div class="param-group">
                            <label for="deleteStoreCoursewareStep-body">StoreCoursewareStepDTO:</label>
                            <textarea id="deleteStoreCoursewareStep-body" placeholder='{"stepId": 1, "storeCoursewareDataId": 1}'></textarea>
                        </div>
                    </div>
                    <button class="test-button" onclick="testApi('deleteStoreCoursewareStep')">测试接口</button>
                    <div class="response-section" id="deleteStoreCoursewareStep-response" style="display: none;">
                        <div class="response-title">响应结果:</div>
                        <div class="response-content" id="deleteStoreCoursewareStep-content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API配置映射
        const apiConfigs = {
            createCoursewareCopy: {
                method: 'POST',
                path: '/teaching/storeCoursewareData/createCoursewareCopy',
                hasBody: true
            },
            listStoreCoursewareSteps: {
                method: 'GET',
                path: '/teaching/storeCoursewareData/listStoreCoursewareSteps',
                hasQuery: true,
                queryParams: ['coursewareId', 'storeId']
            },
            getStoreStepDetails: {
                method: 'GET',
                path: '/teaching/storeCoursewareData/getStoreStepDetails',
                hasQuery: true,
                queryParams: ['storeCoursewareDataId', 'stepId']
            },
            updateStoreStepDetails: {
                method: 'PUT',
                path: '/teaching/storeCoursewareData/updateStoreStepDetails',
                hasBody: true
            },
            updateStoreCoursewareStatus: {
                method: 'PUT',
                path: '/teaching/storeCoursewareData/updateStoreCoursewareStatus',
                hasBody: true
            },
            restoreStoreCoursewareToStandard: {
                method: 'PUT',
                path: '/teaching/storeCoursewareData/restoreStoreCoursewareToStandard',
                hasBody: true
            },
            viewStoreCourseware: {
                method: 'GET',
                path: '/teaching/storeCoursewareData/viewStoreCourseware',
                hasQuery: true,
                queryParams: ['storeCoursewareDataId']
            },
            addStoreCoursewareStep: {
                method: 'POST',
                path: '/teaching/storeCoursewareData/addStoreCoursewareStep',
                hasBody: true
            },
            updateStoreCoursewareStep: {
                method: 'PUT',
                path: '/teaching/storeCoursewareData/updateStoreCoursewareStep',
                hasBody: true
            },
            updateStoreCoursewareStepOrder: {
                method: 'PUT',
                path: '/teaching/storeCoursewareData/updateStoreCoursewareStepOrder',
                hasBody: true
            },
            deleteStoreCoursewareStep: {
                method: 'DELETE',
                path: '/teaching/storeCoursewareData/deleteStoreCoursewareStep',
                hasBody: true
            }
        };

        // 测试API函数
        async function testApi(apiName) {
            const config = apiConfigs[apiName];
            if (!config) {
                alert('未找到API配置: ' + apiName);
                return;
            }

            const baseUrl = document.getElementById('baseUrl').value.trim();
            const authToken = document.getElementById('authToken').value.trim();

            if (!baseUrl) {
                alert('请输入基础URL');
                return;
            }

            // 构建URL
            let url = baseUrl + config.path;

            // 处理路径参数
            if (config.hasPath && config.pathParams) {
                for (const param of config.pathParams) {
                    const value = document.getElementById(`${apiName}-${param}`).value.trim();
                    if (!value) {
                        alert(`请输入${param}参数`);
                        return;
                    }
                    url = url.replace(`{${param}}`, value);
                }
            }

            // 处理查询参数
            if (config.hasQuery && config.queryParams) {
                const queryParams = new URLSearchParams();
                for (const param of config.queryParams) {
                    const value = document.getElementById(`${apiName}-${param}`).value.trim();
                    if (value) {
                        queryParams.append(param, value);
                    }
                }
                if (queryParams.toString()) {
                    url += '?' + queryParams.toString();
                }
            }

            // 构建请求选项
            const options = {
                method: config.method,
                headers: {
                    'Content-Type': 'application/json'
                }
            };

            // 添加认证头
            if (authToken) {
                options.headers['Authorization'] = authToken;
            }

            // 处理请求体
            if (config.hasBody) {
                const bodyElement = document.getElementById(`${apiName}-body`);
                if (bodyElement) {
                    const bodyText = bodyElement.value.trim();
                    if (bodyText) {
                        try {
                            JSON.parse(bodyText); // 验证JSON格式
                            options.body = bodyText;
                        } catch (e) {
                            alert('请求体JSON格式错误: ' + e.message);
                            return;
                        }
                    }
                }
            }

            // 显示响应区域并设置加载状态
            const responseSection = document.getElementById(`${apiName}-response`);
            const responseContent = document.getElementById(`${apiName}-content`);
            const button = event.target;

            responseSection.style.display = 'block';
            responseContent.innerHTML = '<div class="loading">正在发送请求...</div>';
            button.disabled = true;
            button.textContent = '请求中...';

            try {
                const startTime = Date.now();
                const response = await fetch(url, options);
                const endTime = Date.now();

                let responseText = '';
                const contentType = response.headers.get('content-type');

                if (contentType && contentType.includes('application/json')) {
                    const jsonData = await response.json();
                    responseText = JSON.stringify(jsonData, null, 2);
                } else {
                    responseText = await response.text();
                }

                // 构建响应显示内容
                const statusClass = response.ok ? 'success' : 'error';
                const statusCodeClass = `status-${Math.floor(response.status / 100) * 100}`;

                let displayContent = `<div class="${statusClass}">`;
                displayContent += `<span class="status-code ${statusCodeClass}">${response.status}</span>`;
                displayContent += `${response.statusText} (${endTime - startTime}ms)\n\n`;
                displayContent += `<strong>请求URL:</strong> ${url}\n`;
                displayContent += `<strong>请求方法:</strong> ${config.method}\n\n`;

                if (Object.keys(response.headers).length > 0) {
                    displayContent += `<strong>响应头:</strong>\n`;
                    response.headers.forEach((value, key) => {
                        displayContent += `${key}: ${value}\n`;
                    });
                    displayContent += '\n';
                }

                displayContent += `<strong>响应体:</strong>\n${responseText}`;
                displayContent += '</div>';

                responseContent.innerHTML = displayContent;

            } catch (error) {
                responseContent.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            } finally {
                button.disabled = false;
                button.textContent = '测试接口';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('门店课件API测试页面已加载');
        });
    </script>
</body>
</html>
