package com.yuedu.teaching.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.beans.BeanUtils;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yuedu.teaching.mapper.StoreCoursewareStepMapper;
import com.yuedu.teaching.mapper.StoreCoursewareDataMapper;
import com.yuedu.teaching.service.StoreCoursewareStepService;
import com.yuedu.teaching.service.StoreCoursewareDataService;
import com.yuedu.teaching.query.StoreCoursewareStepQuery;
import com.yuedu.teaching.dto.StoreCoursewareStepDTO;
import com.yuedu.teaching.dto.StoreCoursewareStepOrderDTO;
import com.yuedu.teaching.vo.StoreCoursewareStepVO;
import com.yuedu.teaching.entity.StoreCoursewareStep;
import com.yuedu.teaching.entity.StoreCoursewareData;
import com.yuedu.teaching.constant.TeachingConstant;
import com.yuedu.teaching.constant.enums.IsUseEnum;
import com.yuedu.ydsf.common.core.exception.BizException;
import com.yuedu.ydsf.common.security.util.SecurityUtils;
import com.yuedu.ydsf.common.security.util.StoreContextHolder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 门店教学环节表服务层
 *
 * <AUTHOR>
 * @date 2025/08/05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StoreCoursewareStepServiceImpl extends ServiceImpl<StoreCoursewareStepMapper, StoreCoursewareStep>
        implements StoreCoursewareStepService {

    private final StoreCoursewareDataMapper storeCoursewareDataMapper;

    //默认值
    private static final Integer COUNT_UPDATE_NUM = 1;

    /**
     * 新增门店教学环节
     *
     * @param storeCoursewareStepDTO 新增参数
     * @return StoreCoursewareStepVO 新增结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreCoursewareStepVO addStoreCoursewareStep(StoreCoursewareStepDTO storeCoursewareStepDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 验证门店课件数据是否存在且属于当前门店
        StoreCoursewareData storeCoursewareData = validateStoreCoursewareData(
                storeCoursewareStepDTO.getCoursewareDataId(),
                storeCoursewareStepDTO.getCoursewareId(),
                userId, storeId.intValue(), schoolId.intValue());

        // 校验环节类型是否为有效值(1或2)
        if (inValidStepType(storeCoursewareStepDTO.getType().intValue())) {
            throw new BizException("环节类型错误：" + storeCoursewareStepDTO.getType());
        }

        StoreCoursewareStep storeCoursewareStep = BeanUtil.copyProperties(storeCoursewareStepDTO, StoreCoursewareStep.class, "id");
        storeCoursewareStep.setStoreId(storeId.intValue());
        storeCoursewareStep.setSchoolId(schoolId.intValue());
        storeCoursewareStep.setOwner(userId);

        // 更新环节顺序并插入新环节
        this.update(Wrappers.lambdaUpdate(StoreCoursewareStep.class)
                .eq(StoreCoursewareStep::getCoursewareId, storeCoursewareStep.getCoursewareId())
                .eq(StoreCoursewareStep::getCoursewareDataId, storeCoursewareStep.getCoursewareDataId())
                .eq(StoreCoursewareStep::getStepParent, storeCoursewareStep.getStepParent())
                .eq(StoreCoursewareStep::getStoreId, storeId.intValue())
                .eq(StoreCoursewareStep::getSchoolId, schoolId.intValue())
                .eq(StoreCoursewareStep::getOwner, userId)
                .ge(StoreCoursewareStep::getStepOrder, storeCoursewareStep.getStepOrder())
                .setIncrBy(StoreCoursewareStep::getStepOrder, COUNT_UPDATE_NUM)
        );
        this.save(storeCoursewareStep);

        // 设置门店课件发布状态为不可发布
        setStoreCoursewareDataCannotPublish(storeCoursewareData);

        return BeanUtil.copyProperties(storeCoursewareStep, StoreCoursewareStepVO.class);
    }

    /**
     * 修改门店教学环节
     *
     * @param storeCoursewareStepDTO 修改参数
     * @return StoreCoursewareStepVO 修改结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StoreCoursewareStepVO updateStoreCoursewareStep(StoreCoursewareStepDTO storeCoursewareStepDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 验证门店教学环节是否存在且属于当前门店
        StoreCoursewareStep existingStep = validateStoreCoursewareStep(
                storeCoursewareStepDTO.getId(),
                storeCoursewareStepDTO.getCoursewareId(),
                userId, storeId.intValue(), schoolId.intValue());

        // 校验环节类型是否为有效值(1或2)
        if (Objects.nonNull(storeCoursewareStepDTO.getType()) && inValidStepType(storeCoursewareStepDTO.getType().intValue())) {
            throw new BizException("环节类型错误：" + storeCoursewareStepDTO.getType());
        }

        StoreCoursewareStep updatedStep = BeanUtil.copyProperties(storeCoursewareStepDTO, StoreCoursewareStep.class);
        this.updateById(updatedStep);

        // 验证门店课件数据并设置为不可发布
        StoreCoursewareData storeCoursewareData = validateStoreCoursewareData(
                existingStep.getCoursewareDataId(),
                existingStep.getCoursewareId(),
                userId, storeId.intValue(), schoolId.intValue());
        setStoreCoursewareDataCannotPublish(storeCoursewareData);

        return BeanUtil.copyProperties(updatedStep, StoreCoursewareStepVO.class);
    }

    /**
     * 调整门店教学环节顺序
     *
     * @param storeCoursewareStepOrderDTO 环节顺序调整参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStoreCoursewareStepOrder(StoreCoursewareStepOrderDTO storeCoursewareStepOrderDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 验证门店课件数据是否存在且属于当前门店
        StoreCoursewareData storeCoursewareData = validateStoreCoursewareData(
                storeCoursewareStepOrderDTO.getCoursewareDataId(),
                storeCoursewareStepOrderDTO.getCoursewareId(),
                userId, storeId.intValue(), schoolId.intValue());

        // 获取要调整的环节ID列表
        List<Integer> ids = storeCoursewareStepOrderDTO.getInfoList().stream()
                .map(StoreCoursewareStepOrderDTO.Info::getId).toList();

        // 验证所有环节都属于当前门店
        List<StoreCoursewareStep> storeCoursewareStepList = baseMapper.selectList(Wrappers.<StoreCoursewareStep>lambdaQuery()
                .eq(StoreCoursewareStep::getCoursewareId, storeCoursewareStepOrderDTO.getCoursewareId())
                .eq(StoreCoursewareStep::getCoursewareDataId, storeCoursewareStepOrderDTO.getCoursewareDataId())
                .eq(StoreCoursewareStep::getStoreId, storeId.intValue())
                .eq(StoreCoursewareStep::getSchoolId, schoolId.intValue())
                .eq(StoreCoursewareStep::getOwner, userId)
                .in(StoreCoursewareStep::getId, ids));

        if (storeCoursewareStepList.size() != ids.size()) {
            throw new BizException("部分环节不存在或无权限操作");
        }

        // 批量更新环节顺序
        for (StoreCoursewareStepOrderDTO.Info info : storeCoursewareStepOrderDTO.getInfoList()) {
            StoreCoursewareStep storeCoursewareStep = new StoreCoursewareStep();
            storeCoursewareStep.setId(info.getId());
            storeCoursewareStep.setStepParent(info.getStepParent());
            storeCoursewareStep.setStepOrder(info.getStepOrder());
            this.updateById(storeCoursewareStep);
        }

        // 设置门店课件发布状态为不可发布
        setStoreCoursewareDataCannotPublish(storeCoursewareData);
    }

    /**
     * 删除门店教学环节
     *
     * @param storeCoursewareStepDTO 删除参数
     * @return Boolean 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteStoreCoursewareStep(StoreCoursewareStepDTO storeCoursewareStepDTO) {
        // 获取当前用户信息
        Long userId = SecurityUtils.getUser().getId();
        Long storeId = StoreContextHolder.getStoreId();
        Long schoolId = StoreContextHolder.getSchoolId();

        // 验证门店教学环节是否存在且属于当前门店
        StoreCoursewareStep existingStep = validateStoreCoursewareStep(
                storeCoursewareStepDTO.getId(),
                storeCoursewareStepDTO.getCoursewareId(),
                userId, storeId.intValue(), schoolId.intValue());

        // 删除当前环节下的页面（如果是环节类型）
        if (existingStep.getType() == TeachingConstant.COURSEWARE_STEP_TYPE_LINK) {
            this.remove(Wrappers.<StoreCoursewareStep>lambdaQuery()
                    .eq(StoreCoursewareStep::getStepParent, existingStep.getId())
                    .eq(StoreCoursewareStep::getStoreId, storeId.intValue())
                    .eq(StoreCoursewareStep::getSchoolId, schoolId.intValue())
                    .eq(StoreCoursewareStep::getOwner, userId));
        }

        // 验证门店课件数据并设置为不可发布
        StoreCoursewareData storeCoursewareData = validateStoreCoursewareData(
                existingStep.getCoursewareDataId(),
                existingStep.getCoursewareId(),
                userId, storeId.intValue(), schoolId.intValue());
        setStoreCoursewareDataCannotPublish(storeCoursewareData);

        return this.removeById(storeCoursewareStepDTO.getId());
    }

    /**
     * 验证门店课件数据是否存在且属于当前门店
     *
     * @param coursewareDataId 课件数据ID
     * @param coursewareId 课件ID
     * @param userId 用户ID
     * @param storeId 门店ID
     * @param schoolId 校区ID
     * @return StoreCoursewareData
     */
    private StoreCoursewareData validateStoreCoursewareData(Integer coursewareDataId, Integer coursewareId,
                                                           Long userId, Integer storeId, Integer schoolId) {
        StoreCoursewareData storeCoursewareData = storeCoursewareDataMapper.selectOne(Wrappers.<StoreCoursewareData>lambdaQuery()
                .eq(StoreCoursewareData::getId, coursewareDataId)
                .eq(StoreCoursewareData::getCoursewareId, coursewareId)
                .eq(StoreCoursewareData::getStoreId, storeId)
                .eq(StoreCoursewareData::getSchoolId, schoolId)
                .eq(StoreCoursewareData::getOwner, userId));

        if (Objects.isNull(storeCoursewareData)) {
            throw new BizException("门店课件数据不存在或无权限操作");
        }

        return storeCoursewareData;
    }

    /**
     * 验证门店教学环节是否存在且属于当前门店
     *
     * @param stepId 环节ID
     * @param coursewareId 课件ID
     * @param userId 用户ID
     * @param storeId 门店ID
     * @param schoolId 校区ID
     * @return StoreCoursewareStep
     */
    private StoreCoursewareStep validateStoreCoursewareStep(Integer stepId, Integer coursewareId,
                                                           Long userId, Integer storeId, Integer schoolId) {
        StoreCoursewareStep storeCoursewareStep = this.getOne(Wrappers.<StoreCoursewareStep>lambdaQuery()
                .eq(StoreCoursewareStep::getId, stepId)
                .eq(StoreCoursewareStep::getCoursewareId, coursewareId)
                .eq(StoreCoursewareStep::getStoreId, storeId)
                .eq(StoreCoursewareStep::getSchoolId, schoolId)
                .eq(StoreCoursewareStep::getOwner, userId));

        if (Objects.isNull(storeCoursewareStep)) {
            throw new BizException("门店教学环节不存在或无权限操作");
        }

        return storeCoursewareStep;
    }

    /**
     * 设置门店课件数据为不可使用状态
     * 注意：门店课件数据使用isUse字段而不是canPublish字段
     *
     * @param storeCoursewareData 门店课件数据
     */
    private void setStoreCoursewareDataCannotPublish(StoreCoursewareData storeCoursewareData) {
        if (Objects.nonNull(storeCoursewareData) &&
            !Objects.equals(storeCoursewareData.getIsUse(), IsUseEnum.IS_USE_0.code)) {
            StoreCoursewareData updateEntity = new StoreCoursewareData();
            updateEntity.setId(storeCoursewareData.getId());
            updateEntity.setIsUse(IsUseEnum.IS_USE_0.code); // 设置为未使用状态
            storeCoursewareDataMapper.updateById(updateEntity);
        }
    }

    /**
     * 判断环节类型是否合法
     *
     * @param type 环节类型
     * @return 是否合法
     */
    private boolean inValidStepType(int type) {
        return type != TeachingConstant.COURSEWARE_STEP_TYPE_LINK && type != TeachingConstant.COURSEWARE_STEP_TYPE_PAGE;
    }
}
